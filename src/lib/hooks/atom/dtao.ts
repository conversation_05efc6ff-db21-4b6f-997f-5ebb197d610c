import type {
	ColdkeyAlphaSharesAPI,
	ColdkeyAlphaSharesQueryParams,
	HotkeyAlphaSharesAPI,
	HotkeyAlphaSharesQueryParams,
	HotkeyEmissionAPI,
	HotkeyEmissionQueryParams,
	PoolAPI,
	PoolQueryParams,
	StakeBalanceAggregatedAPI,
	StakeBalanceAggregatedQueryParams,
	StakeBalanceAPI,
	StakeBalanceQueryParams,
	SubnetEmissionAPI,
	SubnetEmissionQueryParams,
	DtaoSubnetsAPI,
	ValidatorYieldLatestQueryParams,
	ValidatorYieldLatestAPI,
	SubnetPriceQueryParams,
	TradingviewHistory,
	PoolHistoryQueryParams,
	PoolHistory,
} from "@/types";
import { atom } from "jotai";
import { atomWithQuery } from "jotai-tanstack-query";
import {
	REFETCH_INTERVAL,
	REFETCH_INTERVAL_15S,
	REFETCH_INTERVAL_BY_DAY,
	RETRY_DELAY,
} from "@/lib/config";
import {
	fetchColdkeyAlphaShares,
	fetchHotkeyAlphaShares,
	fetchHotkeyEmission,
	fetchPool,
	fetchStakeBalance,
	fetchStakeBalanceAggregated,
	fetchSubnetEmission,
	fetchDtaoSubnet,
	fetchHeatmapHotkeyNetuid,
	fetchValidatorYieldLatest,
	fetchSubnetPrice,
	fetchTaoStakedAlpha,
	fetchTaoStakedRoot,
} from "../actions/dtao";

export const poolParamsAtom = atom<PoolQueryParams>({
	limit: 10,
});

export const poolAtom = atomWithQuery<PoolAPI>((get) => ({
	queryKey: ["pool", get(poolParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { page, limit, order } = params as PoolQueryParams;

		const fetchedData = await fetchPool({
			page,
			limit,
			order,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL,
	staleTime: REFETCH_INTERVAL,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const subnetEmissionParamsAtom = atom<SubnetEmissionQueryParams>({
	limit: 10,
});

export const subnetEmissionAtom = atomWithQuery<SubnetEmissionAPI>((get) => ({
	queryKey: ["subnet-emission", get(subnetEmissionParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { page, limit, order } = params as SubnetEmissionQueryParams;

		const fetchedData = await fetchSubnetEmission({
			page,
			limit,
			order,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL,
	staleTime: REFETCH_INTERVAL,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const hotkeyEmissionParamsAtom = atom<HotkeyEmissionQueryParams>({
	limit: 10,
});

export const hotkeyEmissionAtom = atomWithQuery<HotkeyEmissionAPI>((get) => ({
	queryKey: ["hotkey-emission", get(hotkeyEmissionParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { page, limit, order } = params as HotkeyEmissionQueryParams;

		const fetchedData = await fetchHotkeyEmission({
			page,
			limit,
			order,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL,
	staleTime: REFETCH_INTERVAL,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const hotkeyAlphaSharesParamsAtom = atom<HotkeyAlphaSharesQueryParams>({
	limit: 10,
});

export const hotkeyAlphaSharesAtom = atomWithQuery<HotkeyAlphaSharesAPI>(
	(get) => ({
		queryKey: ["hotkey-alpha-shares", get(hotkeyAlphaSharesParamsAtom)],
		queryFn: async ({ queryKey: [, params] }) => {
			const { page, limit, order } = params as HotkeyAlphaSharesQueryParams;

			const fetchedData = await fetchHotkeyAlphaShares({
				page,
				limit,
				order,
			});

			return fetchedData;
		},
		initialPageParam: 0,
		refetchInterval: REFETCH_INTERVAL,
		staleTime: REFETCH_INTERVAL,
		retry: (failureCount) => {
			return failureCount < 3;
		},
		retryDelay: RETRY_DELAY,
	})
);

export const coldkeyAlphaSharesParamsAtom = atom<ColdkeyAlphaSharesQueryParams>(
	{
		limit: 10,
	}
);

export const coldkeyAlphaSharesAtom = atomWithQuery<ColdkeyAlphaSharesAPI>(
	(get) => ({
		queryKey: ["coldkey-alpha-shares", get(coldkeyAlphaSharesParamsAtom)],
		queryFn: async ({ queryKey: [, params] }) => {
			const { page, limit, order } = params as ColdkeyAlphaSharesQueryParams;

			const fetchedData = await fetchColdkeyAlphaShares({
				page,
				limit,
				order,
			});

			return fetchedData;
		},
		initialPageParam: 0,
		refetchInterval: REFETCH_INTERVAL,
		staleTime: REFETCH_INTERVAL,
		retry: (failureCount) => {
			return failureCount < 3;
		},
		retryDelay: RETRY_DELAY,
	})
);

export const stakeBalanceParamsAtom = atom<StakeBalanceQueryParams>({
	limit: 10,
});

export const stakeBalanceAtom = atomWithQuery<StakeBalanceAPI>((get) => ({
	queryKey: ["stake-balance", get(stakeBalanceParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { page, limit, order, coldkey, netuid } =
			params as StakeBalanceQueryParams;

		const fetchedData = await fetchStakeBalance({
			netuid,
			coldkey,
			page,
			limit,
			order,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL,
	staleTime: REFETCH_INTERVAL,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const stakeBalanceAggregatedParamsAtom =
	atom<StakeBalanceAggregatedQueryParams>({
		limit: 10,
	});

export const stakeBalanceAggregatedAtom =
	atomWithQuery<StakeBalanceAggregatedAPI>((get) => ({
		queryKey: [
			"stake-balance-aggregated",
			get(stakeBalanceAggregatedParamsAtom),
		],
		queryFn: async ({ queryKey: [, params] }) => {
			const { page, limit, order } =
				params as StakeBalanceAggregatedQueryParams;

			const fetchedData = await fetchStakeBalanceAggregated({
				page,
				limit,
				order,
			});

			return fetchedData;
		},
		initialPageParam: 0,
		refetchInterval: REFETCH_INTERVAL,
		staleTime: REFETCH_INTERVAL,
		retry: (failureCount) => {
			return failureCount < 3;
		},
		retryDelay: RETRY_DELAY,
	}));

export const subnetsParamsAtom = atom<PoolQueryParams>({});

export const subnetsAtom = atomWithQuery<DtaoSubnetsAPI>((get) => ({
	queryKey: ["subnets", get(subnetsParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { page, limit, order, netuid } = params as PoolQueryParams;

		const fetchedData = await fetchDtaoSubnet({
			netuid,
			page,
			limit,
			order,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL_15S,
	staleTime: REFETCH_INTERVAL_15S,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const heatmapHotkeyNetuidAtom = atomWithQuery<{
	[key: string]: { [key: number]: number };
}>((get) => ({
	queryKey: ["heatmap-hotkey-netuid"],
	queryFn: async () => {
		const fetchedData = await fetchHeatmapHotkeyNetuid();

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL_BY_DAY,
	staleTime: REFETCH_INTERVAL_BY_DAY,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const validatorYieldLatestParamsAtom =
	atom<ValidatorYieldLatestQueryParams>({});

export const validatorYieldLatestAtom = atomWithQuery<ValidatorYieldLatestAPI>(
	(get) => ({
		queryKey: ["validator-yield-latest", get(validatorYieldLatestParamsAtom)],
		queryFn: async ({ queryKey: [, params] }) => {
			const { page, limit, order, netuid, min_stake, hotkey } =
				params as ValidatorYieldLatestQueryParams;

			const fetchedData = await fetchValidatorYieldLatest({
				hotkey,
				min_stake,
				netuid,
				page,
				limit,
				order,
			});

			return fetchedData;
		},
		initialPageParam: 0,
		refetchInterval: REFETCH_INTERVAL_BY_DAY,
		staleTime: REFETCH_INTERVAL_BY_DAY,
		retry: (failureCount) => {
			return failureCount < 3;
		},
		retryDelay: RETRY_DELAY,
	})
);

export const subnetPriceParamsAtom = atom<SubnetPriceQueryParams>({
	id: -1,
	type: "",
	timestamp_start: "",
});

export const subnetPriceAtom = atomWithQuery<TradingviewHistory>((get) => ({
	queryKey: ["subnet-price", get(subnetPriceParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { id, timestamp_start, type } = params as SubnetPriceQueryParams;

		if (id === -1 && timestamp_start === "" && type === "") return [];

		const fetchedData = await fetchSubnetPrice({
			id,
			type,
			timestamp_start,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL_BY_DAY,
	staleTime: REFETCH_INTERVAL_BY_DAY,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));

export const taoStakedAlphaParamsAtom = atom<PoolHistoryQueryParams>({});

export const taoStakedAlphaAtom = atomWithQuery<{ [key: string]: number }>(
	(get) => ({
		queryKey: ["tao-staked-alpha", get(taoStakedAlphaParamsAtom)],
		queryFn: async ({ queryKey: [, params] }) => {
			const { frequency, timestamp_start } = params as PoolHistoryQueryParams;

			const fetchedData = await fetchTaoStakedAlpha({
				frequency,
				timestamp_start,
			});

			return fetchedData;
		},
		initialPageParam: 0,
		refetchInterval: REFETCH_INTERVAL_BY_DAY,
		staleTime: REFETCH_INTERVAL_BY_DAY,
		retry: (failureCount) => {
			return failureCount < 3;
		},
		retryDelay: RETRY_DELAY,
	})
);

export const taoStakedRootParamsAtom = atom<PoolHistoryQueryParams>({});

export const taoStakedRootAtom = atomWithQuery<PoolHistory[]>((get) => ({
	queryKey: ["tao-staked-root", get(taoStakedRootParamsAtom)],
	queryFn: async ({ queryKey: [, params] }) => {
		const { frequency, timestamp_start } = params as PoolHistoryQueryParams;

		const fetchedData = await fetchTaoStakedRoot({
			frequency,
			timestamp_start,
		});

		return fetchedData;
	},
	initialPageParam: 0,
	refetchInterval: REFETCH_INTERVAL_BY_DAY,
	staleTime: REFETCH_INTERVAL_BY_DAY,
	retry: (failureCount) => {
		return failureCount < 3;
	},
	retryDelay: RETRY_DELAY,
}));
