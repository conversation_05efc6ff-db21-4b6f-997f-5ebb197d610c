import { createEnv } from "@t3-oss/env-core";
import { z } from "zod";

export const env = createEnv({
	server: {
		API_ENDPOINT_V2: z.string().min(1),
		API_TOKEN: z.string().min(1),
		DASHBOARD_API_URL: z.string().min(1),
		CHARTSETTINGS_URL: z.string().min(1),
	},
	client: {
		NEXT_PUBLIC_DASHBOARD_BASE_URL: z.string().min(1),
		NEXT_PUBLIC_MCP_BASE_URL: z.string().min(1),
		NEXT_PUBLIC_AMPLITUDE_API_KEY: z.string().min(1),
	},
	clientPrefix: "NEXT_PUBLIC_",
	runtimeEnvStrict: {
		API_ENDPOINT_V2: process.env.SERVER_URL_V2,
		API_TOKEN: process.env.API_TOKEN,
		DASHBOARD_API_URL: process.env.DASHBOARD_API_URL,
		CHARTSETTINGS_URL: process.env.CHARTSETTINGS_URL,
		NEXT_PUBLIC_DASHBOARD_BASE_URL: process.env.NEXT_PUBLIC_DASHBOARD_BASE_URL,
		NEXT_PUBLIC_MCP_BASE_URL: process.env.NEXT_PUBLIC_MCP_BASE_URL,
		NEXT_PUBLIC_AMPLITUDE_API_KEY: process.env.NEXT_PUBLIC_AMPLITUDE_API_KEY,
	},
});

export const taoDivider = 1000000000;

export const TOTAL_SUPPLY = "21000000000000000";

export const VALIDATOR_MINIMUM_STAKE = 1024;

// REFETCH_INTERVAL is set to 12 seconds, represented in milliseconds
export const REFETCH_INTERVAL = 12 * 1000;

// REFETCH_INTERVAL_15S is set to 15 seconds, represented in milliseconds
export const REFETCH_INTERVAL_15S = 15 * 1000;

// RETRY_DELAY is set to 1 second, represented in milliseconds
export const RETRY_DELAY = 1000;

// REFETCH_INTERVAL is set to 12 seconds, represented in milliseconds
export const REFETCH_INTERVAL_BY_DAY = 24 * 60 * 60 * 1000;

export const REFETCH_INTERVAL_BY_5_MIN = 5 * 60 * 1000;

export const REFETCH_INTERVAL_BY_20_MIN = 20 * 60 * 1000;

export const REFETCH_INTERVAL_BY_30_MIN = 30 * 60 * 1000;
