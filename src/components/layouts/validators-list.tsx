"use client";

import { BubbleTable } from "@/components/ui/bubble-table";
import cn from "@/lib/cn";
import {
	dtaoValidatorParamsAtom,
	dtaoValidatorsAtom,
} from "@/lib/hooks/atom/validators";
import { AddressFormatter } from "@/lib/utils/address/address-formatter";
import { TableText } from "@/lib/utils/table/table-text";
import type { DtaoValidatorLatest, ColumnSchema, TableData } from "@/types";
import type { ColumnDef, Row, SortingState } from "@tanstack/react-table";
import { useAtom, useSetAtom } from "jotai";
import {
	type Dispatch,
	useCallback,
	useEffect,
	useMemo,
	useState,
} from "react";
import { getCookie, getFilter, getSorting, setCookie } from "@/lib/utils";
import { SubnetDtaoWrapper } from "@/lib/utils/tao/subnet-dtao-wrapper";
import { DtaoPercentage } from "@/lib/utils/tao/dtao-percentage";
import { Md<PERSON>allReceived, MdOutlineNorthEast } from "react-icons/md";
import { useValidatorMetadataAtom } from "@/lib/hooks/atom/use-validator-metadata";
import { taoDivider } from "@/lib/config";
import { format } from "numerable";
import { Bittensor } from "@/components/ui/bittensor";
import { tooltipDescription } from "@/lib/tooltip-description";
import { SubnetTooltipHeader } from "../views/dtao/subnet-tooltip-header";
import { BiSolidStar, BiStar } from "react-icons/bi";
import { TbStarOff } from "react-icons/tb";
import { Text } from "@/components/elements/typography";
import { LOCAL_STORAGE_VALIDATORS_WATCHLIST } from "./sub-menu/validators-menu";
import { useRouter } from "next/navigation";
import { appRoutes } from "@/lib/routes";

const searchFields = ["id", "name"];

export default function ValidatorsListTable({
	initialData: validatorData,
	validatorsWatchList,
	setValidatorsWatchList,
	search,
	watchValidator,
	fromNavMenu = false,
}: {
	initialData: DtaoValidatorLatest[];
	validatorsWatchList: string[];
	setValidatorsWatchList: Dispatch<React.SetStateAction<string[]>>;
	search: string;
	watchValidator: boolean;
	fromNavMenu?: boolean;
}) {
	const router = useRouter();
	const { validatorMetadata } = useValidatorMetadataAtom();
	const [isInit, setIsInit] = useState<boolean>(!!validatorData);
	const [sorting, setSorting] = useState<SortingState>([]);

	const setDtaoValidatorParams = useSetAtom(dtaoValidatorParamsAtom);
	const [{ data, isPending }] = useAtom(dtaoValidatorsAtom);

	const columns: ColumnDef<any, any>[] = useMemo<ColumnSchema[]>(
		() => [
			{
				id: "watch",
				header: () => <></>,
				cell: (info) => (
					<>
						{validatorsWatchList.includes(info.row.original.id) ? (
							<BiSolidStar
								size={16}
								color="#00DBBC"
								className="flex-shrink-0 cursor-pointer"
								onClick={(e) => {
									e.stopPropagation();
									e.preventDefault();
									const watchList: string[] = JSON.parse(
										getCookie(LOCAL_STORAGE_VALIDATORS_WATCHLIST) ?? "[]"
									);
									let newWatchList: string[];

									if (watchList.includes(info.row.original.id)) {
										newWatchList = watchList.filter(
											(netuid) => netuid !== info.row.original.id
										);
										setValidatorsWatchList(newWatchList);
									} else {
										newWatchList = watchList;
										setValidatorsWatchList(newWatchList);
									}

									setCookie(
										LOCAL_STORAGE_VALIDATORS_WATCHLIST,
										JSON.stringify(newWatchList)
									);
								}}
							/>
						) : (
							<BiStar
								size={16}
								className="flex-shrink-0 cursor-pointer opacity-20"
								onClick={(e) => {
									e.stopPropagation();
									e.preventDefault();
									const watchList: string[] = JSON.parse(
										getCookie(LOCAL_STORAGE_VALIDATORS_WATCHLIST) ?? "[]"
									);

									if (!watchList.includes(info.row.original.id)) {
										watchList.push(info.row.original.id);
										setValidatorsWatchList(watchList);
									}

									setCookie(
										LOCAL_STORAGE_VALIDATORS_WATCHLIST,
										JSON.stringify(watchList)
									);
								}}
							/>
						)}
					</>
				),
				enableSorting: false,
			},
			{
				id: "rank",
				header: () => <span className="-mr-2 flex justify-end">Rank</span>,
				cell: (info: any) => (
					<TableText info={info} className="flex w-4 justify-end" />
				),
			},
			{
				id: "id",
				header: () => (
					<span className="-mr-[84px] flex w-44">Name / Address</span>
				),
				cell: (info) => (
					<div className="flex w-44">
						<AddressFormatter
							info={info}
							noColor={false}
							isEncoded
							isValidator
							className="text-sm"
							rootClassName="max-w-44"
							textClassName="flex-shrink-[1]"
							underline="shrink-[unset] min-w-0"
							fromNavMenu={fromNavMenu}
						/>
					</div>
				),
			},
			{
				id: "dominance",
				header: () => (
					<span className="-mr-2 flex w-17 justify-end">Dominance</span>
				),
				cell: (info) => (
					<TableText
						info={info}
						className="!text-white flex w-17 justify-end"
						percentage
					/>
				),
			},
			{
				id: "global_nominators",
				header: () => <span className="-mr-2">Noms</span>,
				cell: (info) => (
					<TableText
						info={info}
						className="!text-white flex w-10 justify-end"
					/>
				),
			},
			{
				id: "global_nominators_24_hr_change",
				header: () => <span className="-mr-2 flex w-16 justify-end">24h</span>,
				cell: (info) => (
					<div className="flex w-16 justify-end">
						<div
							className={cn(
								"flex flex-row items-center justify-center gap-0.5 rounded-full px-2 py-0.5",
								Number(info.getValue()) >= 0 && "bg-[#BAEB471A] text-[#00DBBC]",
								Number(info.getValue()) < 0 && "bg-[#EB53471A] text-[#EB5347]"
							)}
						>
							{Number(info.getValue()) >= 0 ? (
								<MdOutlineNorthEast size={16} />
							) : (
								<MdCallReceived size={16} />
							)}
							<p className="text-xs">{Math.abs(info.getValue())}</p>
						</div>
					</div>
				),
			},
			{
				id: "active_subnets",
				header: () => (
					<SubnetTooltipHeader
						title="Active"
						description={tooltipDescription.validators.active}
					/>
				),
				cell: (info: any) => (
					<TableText info={info} className="flex w-14 justify-end" />
				),
			},
			{
				id: "global_weighted_stake",
				header: () => (
					<SubnetTooltipHeader
						title="Total Weight"
						description={tooltipDescription.validators.totalWeight}
					/>
				),
				cell: (info) => (
					<SubnetDtaoWrapper
						info={info}
						maximumFractionDigits={0}
						minimumFractionDigits={0}
						currencyClassName="-mr-1"
						className="flex w-24 justify-end"
					/>
				),
			},
			{
				id: "global_weighted_stake_24_hr_change",
				header: () => <span className="-mr-2">Weight Change (24h)</span>,
				cell: (info) => (
					<div className="flex w-31 justify-end">
						{info.getValue() !== undefined && (
							<div
								className={cn(
									"flex w-fit flex-row items-center gap-0.5 rounded-full px-2 py-0.5 text-xs",
									info.getValue() >= 0
										? "bg-[#00DBBC1A] text-[#00DBBC]"
										: "bg-[#EB53471A] text-[#EB5347]"
								)}
							>
								{info.getValue() >= 0 ? (
									<MdOutlineNorthEast size={16} />
								) : (
									<MdCallReceived size={16} />
								)}
								{
									<Bittensor
										className={cn(
											"-ml-1 -mr-1.5",
											info.getValue() >= 0 ? "text-[#00DBBC]" : "text-[#EB5347]"
										)}
									/>
								}
								{format(Number(info.getValue()) ?? 0, "0.00a")}
							</div>
						)}
					</div>
				),
			},
			{
				id: "root_stake",
				header: () => (
					<SubnetTooltipHeader
						title="Root Stake"
						description={tooltipDescription.validators.rootStake}
						className="flex w-31 justify-end"
					/>
				),
				cell: (info) => (
					<SubnetDtaoWrapper
						info={info}
						maximumFractionDigits={0}
						minimumFractionDigits={0}
						currencyClassName="-mr-1"
						className="flex w-31 justify-end"
					/>
				),
			},
			{
				id: "weighted_root_stake",
				header: () => (
					<SubnetTooltipHeader
						title="Root Weight (0.18)"
						description={tooltipDescription.validators.rootWeight}
					/>
				),
				cell: (info) => (
					<SubnetDtaoWrapper
						info={info}
						maximumFractionDigits={0}
						minimumFractionDigits={0}
						currencyClassName="-mr-1"
						className="flex w-31 justify-end"
					/>
				),
			},
			{
				id: "global_alpha_stake_as_tao",
				header: () => (
					<SubnetTooltipHeader
						title="Alpha Stake"
						description={tooltipDescription.validators.alphaStake}
					/>
				),
				cell: (info) => (
					<SubnetDtaoWrapper
						info={info}
						maximumFractionDigits={0}
						minimumFractionDigits={0}
						currencyClassName="-mr-1"
						className="flex w-24 justify-end"
					/>
				),
			},
			{
				id: "chart",
				header: "",
				cell: (info) => (
					<DtaoPercentage
						root={info.row.original.weighted_root_stake}
						stake={info.row.original.global_alpha_stake_as_tao}
						leftContent="Root"
						rightContent="Alpha"
						contentClassName="text-sm"
					/>
				),
				enableSorting: false,
			},
			{
				id: "take",
				header: () => (
					<SubnetTooltipHeader
						title="Take"
						description={tooltipDescription.validators.take}
					/>
				),
				cell: (info) => (
					<TableText
						amount={info.getValue() * 100}
						className="!text-white flex w-11 justify-end"
						percentage
					/>
				),
			},
		],
		[fromNavMenu, setValidatorsWatchList, validatorsWatchList]
	);

	const tableData = useMemo(() => {
		const validators = validatorData ? validatorData : data ?? [];

		return (
			validators
				.filter(
					(validator) =>
						(validatorMetadata.find(
							(d) => d.validator_hotkey?.ss58 === validator.hotkey.ss58
						)?.name ?? "") !== ""
				)
				.map((item) => {
					return {
						id: item.hotkey.ss58,
						name:
							validatorMetadata.find(
								(d) => d.validator_hotkey?.ss58 === item.hotkey.ss58
							)?.name ?? "",
						rank: item.rank,
						dominance: Number(item.dominance),
						root_rank: item.root_rank,
						alpha_rank: item.alpha_rank,
						global_nominators: item.global_nominators,
						global_nominators_24_hr_change: item.global_nominators_24_hr_change,
						global_weighted_stake: Number(item.global_weighted_stake),
						weighted_root_stake: Number(item.weighted_root_stake),
						global_alpha_stake_as_tao: Number(item.global_alpha_stake_as_tao),
						take: item.take,
						active_subnets: item.active_subnets,
						root_stake: Number(item.root_stake),
						global_weighted_stake_24_hr_change:
							Number(item.global_weighted_stake_24_hr_change) / taoDivider,
					};
				})
				.sort((a, b) => getSorting(a, b, sorting))
				.filter((it) =>
					watchValidator ? validatorsWatchList.includes(it.id) : it
				)
				.filter((item) => getFilter(item, search, searchFields)) ?? []
		);
	}, [
		data,
		search,
		sorting,
		validatorData,
		validatorMetadata,
		validatorsWatchList,
		watchValidator,
	]);

	const handleFilterChange = useCallback(
		(filter: string, sorting: SortingState) => {
			setSorting(sorting);
		},
		[]
	);

	const handleRowClick = useCallback(
		(event: React.MouseEvent, { original }: Row<TableData>) => {
			const url = appRoutes.validator.detail(original.id as string);
			if (fromNavMenu) {
				// Find and close the navigation menu
				const menuTrigger = document.querySelector(
					'[data-state="open"], [aria-expanded="true"]'
				);
				if (menuTrigger) {
					(menuTrigger as HTMLElement).click();
				}
			}
			if (event.metaKey || event.ctrlKey) {
				window.open(url, "_blank");
			} else {
				router.push(url);
			}
		},
		[fromNavMenu, router]
	);

	useEffect(() => {
		setDtaoValidatorParams({});
	}, [setDtaoValidatorParams]);

	useEffect(() => {
		if (!isPending) {
			setIsInit(false);
		}
	}, [isPending]);

	return (
		<div
			className="flex max-h-[600px] min-h-60 overflow-auto px-10 pb-10"
			style={
				fromNavMenu ? { width: "96vw", minWidth: "96vw", maxWidth: "96vw" } : {}
			}
		>
			{tableData.length > 0 ? (
				<BubbleTable
					small
					link
					columnSchemas={columns}
					rowData={tableData}
					variant="Validator"
					onRowClick={(event, rowInfo) => handleRowClick(event, rowInfo)}
					className="!bg-transparent"
					onFilterChange={handleFilterChange}
					isFetching={isPending && !isInit}
				/>
			) : (
				<div className="flex w-full flex-col items-center justify-center gap-3">
					<div className="flex h-18 w-18 items-center justify-center rounded-full bg-[#323232]">
						<TbStarOff size={24} className="opacity-60" />
					</div>
					<Text level={"xs"} className="font-medium text-[#909090]">
						There are no favorite validators
					</Text>
				</div>
			)}
		</div>
	);
}
