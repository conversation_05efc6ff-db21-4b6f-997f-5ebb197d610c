"use client";

import { <PERSON><PERSON>rde<PERSON>, type DtaoSubnet } from "@/types";
import { Text } from "@/components/elements/typography";
import { useEffect, useMemo, useState } from "react";
import moment from "moment";
import { AddressFormatter } from "@/lib/utils/address/address-formatter";
import { subnetsAtom, subnetsParamsAtom } from "@/lib/hooks/atom/dtao";
import { useAtom, useSetAtom } from "jotai";
import { taoDivider } from "@/lib/config";
import { SubnetCard } from "../subnet-card";
import { SubnetPriceHeaderSection } from "./subnet-price-header-section";
import { useSubnetHeaderOpen } from "@/lib/hooks/single-subnet-header";
import { Button } from "@/components/ui";
import { LuPanelLeftOpen } from "react-icons/lu";
import { MdCallReceived, MdOutlineNorthEast } from "react-icons/md";
import { format } from "numerable";
import cn from "@/lib/cn";
import {
	Too<PERSON><PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageWithFallback } from "@/components/elements/subnet-name-display";
import { tooltipDescription } from "@/lib/tooltip-description";

export const SubnetInfoHeaderCard = ({
	initialData,
	id,
}: {
	initialData: DtaoSubnet[];
	id: string;
}) => {
	const { open, setOpen } = useSubnetHeaderOpen();
	const setSubnetsParams = useSetAtom(subnetsParamsAtom);
	const [isInit, setIsInit] = useState<boolean>(!!initialData);
	const [{ data, isPending }] = useAtom(subnetsAtom);

	const subnetData = useMemo(() => {
		const temp =
			isInit && initialData
				? initialData.find((it) => it.netuid === Number(id))
				: data?.data.find((it) => it.netuid === Number(id));
		return temp;
	}, [isInit, initialData, data, id]);

	useEffect(() => {
		setSubnetsParams({
			netuid: Number(id),
			order: PoolOrder.MarketCapDesc,
		});
	}, [id, setSubnetsParams]);

	useEffect(() => {
		if (!isPending) {
			setIsInit(false);
		}
	}, [isPending]);

	return open ? (
		<div className="w-full px-5 pt-12">
			<div className="flex flex-col gap-4 rounded-xl bg-[#181818] p-4 2xl:flex-row">
				<div className="flex flex-shrink-0 flex-col gap-6">
					<div className="flex flex-row items-start gap-4 overflow-hidden">
						<Button
							variant={"secondary"}
							className="rounded-md border border-[#323232] bg-[#1D1D1D] p-2 hover:bg-[#1D1D1D]/20"
							onClick={() => setOpen(!open)}
						>
							<LuPanelLeftOpen size={24} className="opacity-60" />
						</Button>
						<ImageWithFallback
							netuid={subnetData?.netuid ?? 0}
							height={40}
							width={40}
							alt=""
							className="h-10 w-10 rounded-full object-cover"
						/>
						<div className="flex w-full flex-col gap-1">
							<div className="flex flex-row flex-wrap items-center gap-2">
								<div className="flex flex-row items-center gap-2">
									<Text
										level={"mdTitle"}
										className="!text-2xl !leading-7 whitespace-nowrap font-bold"
									>
										{subnetData?.subnet_name ?? ""}
									</Text>
									{subnetData?.startup_mode && (
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger>
													<Text
														level={"xs"}
														className="whitespace-nowrap rounded-[4px] bg-[#EBC247] px-1 py-0.5 font-medium text-[#000000] leading-[13px]"
													>
														Not Active
													</Text>
												</TooltipTrigger>
												<TooltipContent
													side="bottom"
													className="inline-flex max-w-[260px] flex-col items-start justify-start gap-3 rounded-xl border border-[#FFFFFF]/20 bg-[#2E2E2E] p-4"
												>
													<Text
														level={"xs"}
														className="whitespace-break-spaces font-medium leading-[13px] opacity-60"
													>
														This subnet is registered but inactive. It can be
														started after a 7-day waiting period. No emissions
														occur until activation.
													</Text>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									)}
								</div>
								<Text
									level={"sm"}
									className="flex h-fit flex-row items-center gap-1 whitespace-nowrap rounded-full border border-[#323232] bg-[#1D1D1D] px-3 py-1 font-medium leading-4 opacity-60"
								>
									Rank #{subnetData?.rank ?? ""}
								</Text>
							</div>
							<div className="flex w-full flex-col items-start gap-1 min-[400px]:flex-row min-[400px]:items-center">
								<div className="flex flex-row items-center gap-1">
									<Text level={"base"} className="!leading-5 opacity-60">
										Netuid:
									</Text>
									<Text level={"base"} className="!leading-5 font-fira">
										{subnetData?.netuid ?? ""}
									</Text>
								</div>
								<Text
									level={"base"}
									className="!leading-5 text-[#00DBBC] max-[400px]:hidden"
								>
									/
								</Text>
								<div className="flex flex-row items-center gap-1">
									<Text level={"base"} className="!leading-5 opacity-60">
										Reg:
									</Text>
									<Text level={"base"} className="!leading-5 whitespace-nowrap">
										{moment
											.utc(subnetData?.registration_timestamp)
											.format("DD MMM YYYY")}
									</Text>
								</div>
							</div>
							<div className="w-fit">
								<AddressFormatter
									uid={subnetData?.owner}
									className="font-fira font-medium text-sm leading-5 md:text-base md:leading-6"
									noIcon
								/>
							</div>
						</div>
					</div>
				</div>
				<div className="grid w-full grid-cols-4 gap-4">
					<SubnetPriceHeaderSection
						subnet={subnetData}
						className="col-span-4 lg:col-span-1 sm:col-span-2"
					/>
					<SubnetCard
						title="Market Cap / 24H"
						tooltip
						tooltipDescription={tooltipDescription.subnet.marketCap}
						data={Number(subnetData?.market_cap ?? 0) / taoDivider}
						withUSD
						prefix={"$"}
						suffix={
							<span
								className={cn(
									"ml-3 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full px-2 py-0.5 font-medium text-xs leading-4",
									Number(subnetData?.market_cap_change_1_day) >= 0
										? "bg-[#00DBBC1A] text-[#00DBBC]"
										: "bg-[#EB53471A] text-[#EB5347]"
								)}
							>
								{Number(subnetData?.market_cap_change_1_day) >= 0 ? (
									<MdOutlineNorthEast size={16} />
								) : (
									<MdCallReceived size={16} />
								)}
								{format(subnetData?.market_cap_change_1_day ?? 0, "0.00")}%
							</span>
						}
						className="col-span-4 lg:col-span-1 sm:col-span-2"
					/>
					<SubnetCard
						title="Volume / 24H"
						tooltip
						tooltipDescription={tooltipDescription.subnet.volume}
						data={Number(subnetData?.tao_volume_24_hr ?? 0) / taoDivider}
						withUSD
						prefix={"$"}
						suffix={
							<span
								className={cn(
									"ml-3 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full px-2 py-0.5 font-medium text-xs leading-4",
									Number(subnetData?.tao_volume_24_hr_change_1_day) >= 0
										? "bg-[#00DBBC1A] text-[#00DBBC]"
										: "bg-[#EB53471A] text-[#EB5347]"
								)}
							>
								{Number(subnetData?.tao_volume_24_hr_change_1_day) >= 0 ? (
									<MdOutlineNorthEast size={16} />
								) : (
									<MdCallReceived size={16} />
								)}
								{format(
									Number(subnetData?.tao_volume_24_hr_change_1_day),
									"0.00"
								)}
								%
							</span>
						}
						className="col-span-4 lg:col-span-1 sm:col-span-2"
					/>
					<SubnetCard
						title="Emissions"
						tooltip
						tooltipDescription={tooltipDescription.subnet.emission}
						data={Number(subnetData?.emission ?? 0) / 10000000}
						suffix={<p className="ml-0.5 font-normal text-base leading-5">%</p>}
						className="col-span-4 lg:col-span-1 sm:col-span-2"
					/>
				</div>
			</div>
		</div>
	) : (
		<></>
	);
};
