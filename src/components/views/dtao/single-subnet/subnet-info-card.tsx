"use client";

import { <PERSON><PERSON>rder, type DtaoSubnet } from "@/types";
import { Text } from "@/components/elements/typography";
import { SubnetCardGradient } from "./subnet-card-gradient";
import { SubnetDataSection } from "./subnet-data-section";
import { SubnetFinancialSection } from "./subnet-financial-section";
import { SubnetPriceSection } from "./subnet-price-section";
import { useEffect, useMemo, useState } from "react";
import { SubnetTradingDataSection } from "./subnet-trading-data";
import { SubnetSocialSection } from "./subnet-social-section";
import moment from "moment";
import { AddressFormatter } from "@/lib/utils/address/address-formatter";
import { subnetsAtom, subnetsParamsAtom } from "@/lib/hooks/atom/dtao";
import { useAtom, useSetAtom } from "jotai";
import { useSubnetHeaderOpen } from "@/lib/hooks/single-subnet-header";
import { Button } from "@/components/ui/button";
import { LuPanelLeftOpen } from "react-icons/lu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { ImageWithFallback } from "@/components/elements/subnet-name-display";

export const SubnetInfoCard = ({
	initialData,
	id,
}: {
	initialData: DtaoSubnet[];
	id: string;
}) => {
	const { open, setOpen } = useSubnetHeaderOpen();
	const setSubnetsParams = useSetAtom(subnetsParamsAtom);
	const [isInit, setIsInit] = useState<boolean>(!!initialData);
	const [{ data, isPending }] = useAtom(subnetsAtom);

	const subnetData = useMemo(() => {
		const temp =
			isInit && initialData
				? initialData.find((it) => it.netuid === Number(id))
				: data?.data.find((it) => it.netuid === Number(id));
		return temp;
	}, [isInit, initialData, data, id]);

	const shortDescription = useMemo(() => {
		const sentences = (subnetData?.description ?? "").split(".");

		return `${sentences.slice(0, 2).join(".")}${
			sentences.length < 3 ? "" : "."
		}`;
	}, [subnetData]);

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		setSubnetsParams({
			order: PoolOrder.MarketCapDesc,
		});
	}, [id, setSubnetsParams]);

	useEffect(() => {
		if (!isPending) {
			setIsInit(false);
		}
	}, [isPending]);

	return open ? (
		<></>
	) : (
		<div className="relative flex h-max w-[546px] flex-shrink-0 flex-col gap-10 overflow-hidden rounded-xl bg-[#181818] p-4 min-[1152px]:sticky min-[1152px]:top-[152px] max-[1152px]:w-full">
			<div className="flex flex-col gap-6">
				<div className="flex flex-row items-start gap-4 overflow-hidden">
					<ImageWithFallback
						netuid={subnetData?.netuid ?? 0}
						height={40}
						width={40}
						alt=""
						className="h-10 w-10 rounded-full object-cover"
					/>
					<div className="flex w-full flex-col gap-1">
						<div className="flex flex-row items-center justify-between gap-2 sm:gap-6">
							<div className="flex w-full flex-row flex-wrap items-center justify-between gap-2">
								<div className="flex flex-row items-center gap-2">
									<Text
										level={"mdTitle"}
										className="font-bold text-2xl leading-7"
									>
										{subnetData?.subnet_name ?? ""}
									</Text>
									{subnetData?.startup_mode && (
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger>
													<Text
														level={"xs"}
														className="whitespace-nowrap rounded-[4px] bg-[#EBC247] px-1 py-0.5 font-medium text-[#000000] leading-[13px]"
													>
														Not Active
													</Text>
												</TooltipTrigger>
												<TooltipContent
													side="bottom"
													className="inline-flex max-w-[260px] flex-col items-start justify-start gap-3 rounded-xl border border-[#FFFFFF]/20 bg-[#2E2E2E] p-4"
												>
													<Text
														level={"xs"}
														className="whitespace-break-spaces font-medium leading-[13px] opacity-60"
													>
														This subnet is registered but inactive. It can be
														started after a 7-day waiting period. No emissions
														occur until activation.
													</Text>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									)}
								</div>
								<Text
									level={"sm"}
									className="flex h-fit flex-row items-center gap-1 whitespace-nowrap rounded-full border border-[#323232] bg-[#1D1D1D] px-3 py-2.5 font-medium leading-4 opacity-60"
								>
									Rank #{subnetData?.rank ?? ""}
								</Text>
							</div>
							<Button
								variant={"secondary"}
								className="rounded-md border border-[#323232] bg-[#1D1D1D] p-2 hover:bg-[#1D1D1D]/20"
								onClick={() => setOpen(!open)}
							>
								<LuPanelLeftOpen size={24} className="rotate-180 opacity-60" />
							</Button>
						</div>
						<div className="flex w-full flex-row items-center gap-1">
							<Text level={"base"} className="opacity-60">
								Netuid:
							</Text>
							<Text level={"base"} className="font-fira">
								{subnetData?.netuid ?? ""}
							</Text>
							<Text level={"base"} className="text-[#00DBBC]">
								/
							</Text>
							<Text level={"base"} className="opacity-60">
								Reg:
							</Text>
							<Text level={"base"}>
								{moment
									.utc(subnetData?.registration_timestamp)
									.format("DD MMM YYYY")}
							</Text>
						</div>
						<div className="w-fit">
							<AddressFormatter
								uid={subnetData?.owner}
								className="font-fira font-medium text-sm leading-5 md:text-base md:leading-6"
								noIcon
							/>
						</div>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<Text level={"base"} className="opacity-60">
						{shortDescription}
					</Text>
					<SubnetSocialSection
						url={subnetData?.github ?? ""}
						subnetContact={subnetData?.subnet_contact ?? ""}
						subnetUrl={subnetData?.subnet_url ?? ""}
						discord={subnetData?.discord_url ?? ""}
					/>
				</div>
			</div>
			<SubnetPriceSection subnet={subnetData} />
			<SubnetFinancialSection subnet={subnetData} />
			<SubnetTradingDataSection subnet={subnetData} />
			<SubnetDataSection subnet={subnetData} />
			<SubnetCardGradient />
		</div>
	);
};
